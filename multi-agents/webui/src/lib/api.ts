import { ArticleRequest, MathRequest, SSEEvent } from '@/types'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api'

// 通用API客户端
class APIClient {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // SSE流式请求
  async *streamPost(endpoint: string, data: any): AsyncGenerator<SSEEvent, void, unknown> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('Failed to get response reader')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6))
              yield {
                ...eventData,
                timestamp: eventData.timestamp || new Date().toISOString()
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE event:', line, parseError)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }
}

// 创建API客户端实例
export const apiClient = new APIClient(API_BASE_URL)

// 具体的API方法
export const api = {
  // 健康检查
  health: () => apiClient.get('/health'),
  
  // API信息
  info: () => apiClient.get('/'),
  
  // 文章评分
  reviewArticle: (data: ArticleRequest) =>
    apiClient.streamPost('/v1/article/review', data),

  // 数学解题
  aiMath: (data: MathRequest) =>
    apiClient.streamPost('/v1/math/solve', data),
}
