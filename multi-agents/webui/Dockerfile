# Local/Dev
FROM node:18-alpine AS builder
# Production
# FROM harbor.xiaoxingcloud.com/foundation/node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# Local/Dev
FROM node:18-alpine AS production
# Production
# FROM harbor.xiaoxingcloud.com/foundation/node:18-alpine AS production

# 安装serve用于提供静态文件服务
RUN npm install -g serve

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 创建非root用户
RUN addgroup -g 1001 -S nodejs \
    && adduser -S nextjs -u 1001

# 更改文件所有权
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["serve", "-s", "dist", "-l", "3000"]
