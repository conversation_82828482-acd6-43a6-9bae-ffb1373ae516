services:
  # 后端API服务
  backend:
    build:
      context: .
    container_name: multi-agents-backend
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY:-}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY:-}
      - LANGFUSE_BASE_URL=${LANGFUSE_BASE_URL:-}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  # 前端Web服务
  frontend:
    build:
      context: ./webui
      dockerfile: Dockerfile
      target: production
    container_name: multi-agents-frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8081/api
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: multi-agents-nginx
    ports:
      - "8081:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
